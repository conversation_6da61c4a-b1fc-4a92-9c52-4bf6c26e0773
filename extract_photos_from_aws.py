#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to extract all player submission photos from the AWS EC2 database.
This script provides multiple methods to safely retrieve photos without risking the database.

Usage:
    python extract_photos_from_aws.py [method]
    
Methods:
    1. ssh_rsync - Use rsync over SSH to copy files (recommended)
    2. ssh_tar - Create tar archive on server and download via SSH
    3. database_export - Export database info and download files individually
    4. git_upload - Upload files to git repository (if repo is accessible)
"""

import os
import sys
import subprocess
import json
import sqlite3
from pathlib import Path
import argparse

# Configuration - UPDATE THESE VALUES
AWS_HOST = "your-ec2-instance-ip-or-hostname"
AWS_USER = "ec2-user"  # or ubuntu, depending on your AMI
AWS_KEY_PATH = "~/.ssh/your-key.pem"  # Path to your SSH key
REMOTE_PROJECT_PATH = "/path/to/bimbo-hunter-base"  # Update this path
LOCAL_EXTRACT_DIR = "./extracted_photos"

def ensure_local_directory():
    """Create local directory for extracted photos"""
    Path(LOCAL_EXTRACT_DIR).mkdir(exist_ok=True)
    print(f"Created local directory: {LOCAL_EXTRACT_DIR}")

def method_ssh_rsync():
    """
    Method 1: Use rsync over SSH to copy all user images
    This is the safest and most efficient method.
    """
    print("Using rsync over SSH to copy user images...")
    ensure_local_directory()
    
    # Rsync command to copy user-images directory
    remote_path = f"{AWS_USER}@{AWS_HOST}:{REMOTE_PROJECT_PATH}/client/public/user-images/"
    local_path = f"{LOCAL_EXTRACT_DIR}/user-images/"
    
    rsync_cmd = [
        "rsync", "-avz", "--progress",
        "-e", f"ssh -i {AWS_KEY_PATH}",
        remote_path, local_path
    ]
    
    print(f"Running: {' '.join(rsync_cmd)}")
    try:
        result = subprocess.run(rsync_cmd, check=True, capture_output=True, text=True)
        print("Rsync completed successfully!")
        print(f"Files copied to: {local_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Rsync failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def method_ssh_tar():
    """
    Method 2: Create tar archive on server and download via SSH
    Good for slower connections or when rsync isn't available.
    """
    print("Creating tar archive on server and downloading...")
    ensure_local_directory()
    
    # Create tar archive on remote server
    remote_tar_path = f"{REMOTE_PROJECT_PATH}/user_images_backup.tar.gz"
    tar_cmd = f"cd {REMOTE_PROJECT_PATH} && tar -czf user_images_backup.tar.gz client/public/user-images/"
    
    ssh_tar_cmd = [
        "ssh", "-i", AWS_KEY_PATH, f"{AWS_USER}@{AWS_HOST}", tar_cmd
    ]
    
    print("Creating tar archive on remote server...")
    try:
        subprocess.run(ssh_tar_cmd, check=True)
        print("Tar archive created successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Failed to create tar archive: {e}")
        return False
    
    # Download tar archive
    local_tar_path = f"{LOCAL_EXTRACT_DIR}/user_images_backup.tar.gz"
    scp_cmd = [
        "scp", "-i", AWS_KEY_PATH,
        f"{AWS_USER}@{AWS_HOST}:{remote_tar_path}",
        local_tar_path
    ]
    
    print("Downloading tar archive...")
    try:
        subprocess.run(scp_cmd, check=True)
        print("Download completed!")
    except subprocess.CalledProcessError as e:
        print(f"Failed to download tar archive: {e}")
        return False
    
    # Extract tar archive locally
    print("Extracting tar archive...")
    try:
        subprocess.run(["tar", "-xzf", local_tar_path, "-C", LOCAL_EXTRACT_DIR], check=True)
        print(f"Files extracted to: {LOCAL_EXTRACT_DIR}")
        
        # Clean up tar file
        os.remove(local_tar_path)
        print("Cleaned up tar archive")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to extract tar archive: {e}")
        return False

def method_database_export():
    """
    Method 3: Export database information and download files individually
    Most thorough method that also provides metadata about uploads.
    """
    print("Exporting database information and downloading files...")
    ensure_local_directory()
    
    # First, copy the database file to examine it
    db_local_path = f"{LOCAL_EXTRACT_DIR}/bhunter.db"
    scp_db_cmd = [
        "scp", "-i", AWS_KEY_PATH,
        f"{AWS_USER}@{AWS_HOST}:{REMOTE_PROJECT_PATH}/bhunter.db",
        db_local_path
    ]
    
    print("Downloading database file...")
    try:
        subprocess.run(scp_db_cmd, check=True)
        print("Database downloaded successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Failed to download database: {e}")
        return False
    
    # Analyze database and create file list
    print("Analyzing database for user images...")
    try:
        conn = sqlite3.connect(db_local_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get all progress records with user images
        cursor.execute("""
            SELECT p.user_id, p.board_id, p.user_images, u.display_name
            FROM progress p
            JOIN users u ON p.user_id = u.id
            WHERE p.user_images != '{}' AND p.user_images IS NOT NULL
        """)
        
        records = cursor.fetchall()
        file_list = []
        
        for record in records:
            user_images = json.loads(record['user_images'])
            for square_index, image_path in user_images.items():
                # Convert relative path to actual file path
                if image_path.startswith('/user-images/'):
                    file_path = f"client/public{image_path}"
                    file_list.append({
                        'remote_path': f"{REMOTE_PROJECT_PATH}/{file_path}",
                        'local_path': f"{LOCAL_EXTRACT_DIR}/{file_path}",
                        'user_id': record['user_id'],
                        'board_id': record['board_id'],
                        'display_name': record['display_name'],
                        'square_index': square_index
                    })
        
        conn.close()
        
        print(f"Found {len(file_list)} user images to download")
        
        # Create metadata file
        metadata_path = f"{LOCAL_EXTRACT_DIR}/image_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(file_list, f, indent=2)
        print(f"Metadata saved to: {metadata_path}")
        
        # Download each file
        success_count = 0
        for i, file_info in enumerate(file_list):
            print(f"Downloading {i+1}/{len(file_list)}: {file_info['remote_path']}")
            
            # Ensure local directory exists
            local_dir = os.path.dirname(file_info['local_path'])
            Path(local_dir).mkdir(parents=True, exist_ok=True)
            
            scp_cmd = [
                "scp", "-i", AWS_KEY_PATH,
                f"{AWS_USER}@{AWS_HOST}:{file_info['remote_path']}",
                file_info['local_path']
            ]
            
            try:
                subprocess.run(scp_cmd, check=True, capture_output=True)
                success_count += 1
            except subprocess.CalledProcessError:
                print(f"  Failed to download: {file_info['remote_path']}")
        
        print(f"Successfully downloaded {success_count}/{len(file_list)} files")
        return True
        
    except Exception as e:
        print(f"Database analysis failed: {e}")
        return False

def method_git_upload():
    """
    Method 4: Upload files to git repository (if accessible from EC2)
    Only use this if your git repo has enough space and you want version control.
    """
    print("This method requires manual setup on the EC2 instance.")
    print("You would need to:")
    print("1. SSH into your EC2 instance")
    print("2. Navigate to your project directory")
    print("3. Add user-images to git: git add client/public/user-images/")
    print("4. Commit: git commit -m 'Backup user images'")
    print("5. Push: git push")
    print("6. Then pull the changes locally")
    print("\nNote: This may not be suitable for large files or many images.")
    return False

def main():
    parser = argparse.ArgumentParser(description="Extract photos from AWS EC2 bimbo-hunter database")
    parser.add_argument("method", nargs="?", default="ssh_rsync", 
                       choices=["ssh_rsync", "ssh_tar", "database_export", "git_upload"],
                       help="Method to use for extraction")
    
    args = parser.parse_args()
    
    print("=== Bimbo Hunter Photo Extraction Tool ===")
    print(f"Method: {args.method}")
    print(f"Target directory: {LOCAL_EXTRACT_DIR}")
    print()
    
    # Check configuration
    print("Please update the configuration variables at the top of this script:")
    print(f"  AWS_HOST: {AWS_HOST}")
    print(f"  AWS_USER: {AWS_USER}")
    print(f"  AWS_KEY_PATH: {AWS_KEY_PATH}")
    print(f"  REMOTE_PROJECT_PATH: {REMOTE_PROJECT_PATH}")
    print()
    
    if AWS_HOST == "your-ec2-instance-ip-or-hostname":
        print("ERROR: Please update the configuration variables before running!")
        return 1
    
    # Execute selected method
    methods = {
        "ssh_rsync": method_ssh_rsync,
        "ssh_tar": method_ssh_tar,
        "database_export": method_database_export,
        "git_upload": method_git_upload
    }
    
    success = methods[args.method]()
    
    if success:
        print(f"\n✅ Photo extraction completed successfully!")
        print(f"Photos are now available in: {LOCAL_EXTRACT_DIR}")
    else:
        print(f"\n❌ Photo extraction failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
